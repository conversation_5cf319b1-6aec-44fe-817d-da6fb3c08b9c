package affiliate

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

// AffiliateService handles business logic for affiliate transactions
type AffiliateService struct {
	affiliateRepo           repo.AffiliateRepositoryInterface
	userRepo                transaction.UserRepositoryInterface
	activityCashbackService *ActivityCashbackService
	memeCommissionService   *MemeCommissionService
	taskProcessorManager    *activity_cashback.TaskProcessorManager

	// SOL price tracking (thread-safe)
	priceMutex         sync.RWMutex
	latestSolPrice     decimal.Decimal
	latestSolPriceTime time.Time
}

// NewAffiliateService creates a new affiliate service
func NewAffiliateService() AffiliateServiceInterface {
	activityService := activity_cashback.NewActivityCashbackService()
	service := &AffiliateService{
		affiliateRepo:           repo.NewAffiliateRepository(),
		userRepo:                transaction.NewUserRepository(),
		activityCashbackService: NewActivityCashbackService(),
		memeCommissionService:   NewMemeCommissionService(),
		taskProcessorManager:    activity_cashback.NewTaskProcessorManager(activityService),
	}

	// Initialize SOL price from database on startup
	service.initializeSolPriceFromDatabase()

	return service
}

// initializeSolPriceFromDatabase initializes the SOL price from database on service startup
func (s *AffiliateService) initializeSolPriceFromDatabase() {
	ctx := context.Background()
	priceSnapshot, err := s.affiliateRepo.GetLatestSolPrice(ctx)
	if err != nil {
		global.GVA_LOG.Warn("Failed to initialize SOL price from database on startup",
			zap.Error(err))
		return
	}

	s.priceMutex.Lock()
	s.latestSolPrice = priceSnapshot.Price
	s.latestSolPriceTime = priceSnapshot.Timestamp
	s.priceMutex.Unlock()

	global.GVA_LOG.Info("Initialized SOL price from database on startup",
		zap.String("price", priceSnapshot.Price.String()),
		zap.Time("timestamp", priceSnapshot.Timestamp))
}

// ProcessAffiliateTransaction processes an affiliate transaction event from NATS
func (s *AffiliateService) ProcessAffiliateTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	global.GVA_LOG.Info("Processing affiliate transaction",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("user_id", txEvent.UserId),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("status", string(txEvent.Status)),
		zap.String("raw_txid", txEvent.Txid),     // Log raw txid from NATS
		zap.Bool("has_txid", txEvent.Txid != "")) // Log whether txid exists

	// Check if transaction already exists
	existingTx, err := s.affiliateRepo.GetAffiliateTransactionByOrderID(ctx, txEvent.ID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing transaction: %w", err)
	}

	// If transaction exists, update it
	if existingTx != nil {
		return s.updateExistingTransaction(ctx, existingTx, txEvent)
	}

	// Create new transaction
	return s.createNewTransaction(ctx, txEvent)
}

// ProcessSolPriceUpdate processes a SOL price update event from NATS
// Note: This method now only stores the price in memory for later use when affiliate transactions occur
func (s *AffiliateService) ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsModel.SolPriceEvent) error {
	global.GVA_LOG.Debug("Processing SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	// Store the latest price in memory for use when affiliate transactions occur
	// We don't save to database here anymore - only when affiliate transactions happen
	s.priceMutex.Lock()
	s.latestSolPrice = priceEvent.UsdPrice
	s.latestSolPriceTime = priceEvent.GetTime()
	s.priceMutex.Unlock()

	global.GVA_LOG.Debug("SOL price updated in memory",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	return nil
}

// createNewTransaction creates a new affiliate transaction
func (s *AffiliateService) createNewTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	// Validate user ID
	if txEvent.UserId == "" {
		return fmt.Errorf("user ID is empty")
	}

	// Parse user ID
	userID, err := uuid.Parse(txEvent.UserId)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Get referral information
	var referrerID *uuid.UUID
	var referralDepth int
	var commissionRate decimal.Decimal
	var commissionAmount decimal.Decimal

	// Try to get referral info directly (this will also validate user exists)
	referralInfo, err := s.userRepo.GetReferralInfo(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Debug("No referral info found for user, creating transaction without referral",
				zap.String("user_id", txEvent.UserId))
		} else {
			global.GVA_LOG.Warn("Failed to get referral info, creating transaction without referral",
				zap.String("user_id", txEvent.UserId),
				zap.Error(err))
		}
	} else if referralInfo != nil && referralInfo.ReferrerID != nil {
		referrerID = referralInfo.ReferrerID
		referralDepth = referralInfo.Depth

		// Calculate commission (example: 0.1% for direct referrals)
		commissionRate = decimal.NewFromFloat(0.001) // 0.1%
		commissionAmount = txEvent.QuoteAmount.Mul(commissionRate)

		global.GVA_LOG.Debug("Found referral info for transaction",
			zap.String("user_id", txEvent.UserId),
			zap.String("referrer_id", referrerID.String()),
			zap.Int("depth", referralDepth),
			zap.String("commission_amount", commissionAmount.String()))
	}

	// Convert NATS model types to database model types
	transactionType := model.TransactionType(txEvent.TransactionType)
	orderType := model.OrderType(txEvent.Type)
	status := model.TransactionStatus(txEvent.Status)

	// Calculate volume in USD using SOL price
	volumeUSD, err := s.calculateVolumeUSD(ctx, txEvent.QuoteAmount)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate volume USD, using quote amount as fallback",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("quote_amount", txEvent.QuoteAmount.String()),
			zap.Error(err))
		// Fallback: use quote_amount as volume_usd (this maintains backward compatibility)
		volumeUSD = txEvent.QuoteAmount
	}

	// Handle TxHash - use order_id if txHash is empty to avoid duplicate key errors
	txHash := s.getTxHashWithLogging(txEvent)

	// Create affiliate transaction
	affiliateTx := &model.AffiliateTransaction{
		OrderID:           txEvent.ID,
		CreatedAt:         txEvent.CreatedAt,
		TransactionType:   transactionType,
		Type:              orderType,
		ChainID:           txEvent.ChainId,
		BaseAddress:       txEvent.BaseAddress,
		BaseSymbol:        txEvent.BaseSymbol,
		QuoteAddress:      txEvent.QuoteAddress,
		QuoteSymbol:       txEvent.QuoteSymbol,
		UserID:            userID,
		UserAddress:       txEvent.UserAddress,
		BaseAmount:        txEvent.BaseAmount,
		QuoteAmount:       txEvent.QuoteAmount,
		TotalFee:          txEvent.TotalFee,
		PlatformFee:       txEvent.PlatformFee,
		PlatformFeeAmount: txEvent.PlatformFeeAmount,
		PlatformFeeMint:   txEvent.PlatformFeeMint,
		FeeRate:           txEvent.FeeRate,
		Slippage:          txEvent.Slippage,
		Status:            status,
		TxHash:            txHash, // Use processed txHash
		MevProtect:        txEvent.MevProtect,
		ReferrerID:        referrerID,
		ReferralDepth:     referralDepth,
		CommissionRate:    commissionRate,
		CommissionAmount:  commissionAmount,
		CommissionPaid:    false,
		VolumeUSD:         volumeUSD,
	}

	if err := s.affiliateRepo.CreateAffiliateTransaction(ctx, affiliateTx); err != nil {
		// Check if it's a duplicate key error
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			global.GVA_LOG.Warn("Duplicate transaction detected, skipping",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("tx_hash", txHash),
				zap.Error(err))
			return nil // Skip duplicate transactions gracefully
		}
		return fmt.Errorf("failed to create affiliate transaction: %w", err)
	}

	// Save SOL price snapshot when affiliate transaction is created
	if err := s.saveSolPriceSnapshot(ctx); err != nil {
		global.GVA_LOG.Warn("Failed to save SOL price snapshot",
			zap.String("order_id", txEvent.ID.String()),
			zap.Error(err))
		// Don't fail the transaction creation if price snapshot fails
	}

	global.GVA_LOG.Info("Start affiliate transaction",
		zap.String("affiliateTx.Status", string(affiliateTx.Status)),
		zap.String("affiliateTx.QuoteAmount", affiliateTx.QuoteAmount.String()),
	)

	// Process activity transaction cashback if transaction is completed
	if affiliateTx.Status == "Completed" {
		// Calculate cashback amount USD - using platform fee amount as the base for commission calculation
		commissionAmountUSD := affiliateTx.PlatformFee
		if err := s.memeCommissionService.ProcessMemeCommission(ctx, affiliateTx, commissionAmountUSD); err != nil {
			global.GVA_LOG.Error("Failed to process meme commission",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction creation if cashback processing fails
		}

		if err := s.activityCashbackService.ProcessActivityTransactionCashback(ctx, affiliateTx); err != nil {
			global.GVA_LOG.Error("Failed to process activity transaction cashback",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction creation if cashback processing fails
		}

		// Process trading task completion for MEME trades directly from NATS event
		if err := s.processMemeTradeTaskCompletionFromNATS(ctx, txEvent); err != nil {
			global.GVA_LOG.Error("Failed to process MEME trade task completion from NATS",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction creation if task processing fails
		}
	}

	global.GVA_LOG.Info("Affiliate transaction created successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", affiliateTx.ID),
		zap.String("commission_amount", commissionAmount.String()))

	return nil
}

// updateExistingTransaction updates an existing affiliate transaction
func (s *AffiliateService) updateExistingTransaction(ctx context.Context, existingTx *model.AffiliateTransaction, txEvent *natsModel.AffiliateTxEvent) error {
	// Update fields that might change
	existingTx.Status = model.TransactionStatus(txEvent.Status)

	// Handle TxHash - use order_id if txHash is empty to avoid duplicate key errors
	// Only update TxHash if the new value is not empty, to preserve existing fallback values
	if txEvent.Txid != "" {
		// Check if we're overwriting a fallback value (order_id) with a real tx_hash
		if existingTx.TxHash == txEvent.ID.String() {
			global.GVA_LOG.Info("Updating tx_hash from order_id fallback to actual tx_hash",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("old_tx_hash", existingTx.TxHash),
				zap.String("new_tx_hash", txEvent.Txid))
		}
		existingTx.TxHash = txEvent.Txid
	} else if existingTx.TxHash == "" {
		// If both current and new TxHash are empty, use order_id as fallback
		existingTx.TxHash = txEvent.ID.String()
		global.GVA_LOG.Warn("Using order_id as tx_hash fallback during update",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("status", string(txEvent.Status)),
			zap.String("reason", "both_current_and_new_txid_empty"))
	} else {
		// Preserve existing tx_hash if new one is empty (don't overwrite real tx_hash with order_id)
		global.GVA_LOG.Debug("Preserving existing tx_hash, new txid is empty",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("existing_tx_hash", existingTx.TxHash),
			zap.String("status", string(txEvent.Status)))
	}

	existingTx.BaseAmount = txEvent.BaseAmount
	existingTx.QuoteAmount = txEvent.QuoteAmount
	existingTx.TotalFee = txEvent.TotalFee

	// Recalculate volume in USD using SOL price
	volumeUSD, err := s.calculateVolumeUSD(ctx, txEvent.QuoteAmount)
	if err != nil {
		global.GVA_LOG.Error("Failed to calculate volume USD for update, using quote amount as fallback",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("quote_amount", txEvent.QuoteAmount.String()),
			zap.Error(err))
		// Fallback: use quote_amount as volume_usd (this maintains backward compatibility)
		volumeUSD = txEvent.QuoteAmount
	}
	existingTx.VolumeUSD = volumeUSD

	// Recalculate commission if transaction is completed and commission hasn't been paid
	if existingTx.Status == model.StatusCompleted && !existingTx.CommissionPaid && existingTx.ReferrerID != nil {
		existingTx.CommissionAmount = existingTx.VolumeUSD.Mul(existingTx.CommissionRate)
	}

	if err := s.affiliateRepo.UpdateAffiliateTransaction(ctx, existingTx); err != nil {
		// Check if it's a duplicate key error
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			global.GVA_LOG.Warn("Duplicate transaction detected during update, skipping",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("tx_hash", existingTx.TxHash),
				zap.Error(err))
			return nil // Skip duplicate transactions gracefully
		}
		return fmt.Errorf("failed to update affiliate transaction: %w", err)
	}

	// Process activity transaction cashback if transaction status changed to completed
	if existingTx.Status == model.StatusCompleted {
		// Calculate cashback amount USD - using platform fee amount as the base for commission calculation
		cashbackAmountUSD := existingTx.PlatformFeeAmount
		if err := s.memeCommissionService.ProcessMemeCommission(ctx, existingTx, cashbackAmountUSD); err != nil {
			global.GVA_LOG.Error("Failed to process meme commission",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction update if cashback processing fails
		}

		if err := s.activityCashbackService.ProcessActivityTransactionCashback(ctx, existingTx); err != nil {
			global.GVA_LOG.Error("Failed to process activity transaction cashback",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction update if cashback processing fails
		}

		// Process trading task completion for MEME trades
		if err := s.processMemeTradeTaskCompletion(ctx, existingTx); err != nil {
			global.GVA_LOG.Error("Failed to process MEME trade task completion",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction update if task processing fails
		}
	}

	// Save SOL price snapshot when affiliate transaction is updated
	if err := s.saveSolPriceSnapshot(ctx); err != nil {
		global.GVA_LOG.Warn("Failed to save SOL price snapshot",
			zap.String("order_id", txEvent.ID.String()),
			zap.Error(err))
		// Don't fail the transaction update if price snapshot fails
	}

	global.GVA_LOG.Info("Affiliate transaction updated successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", existingTx.ID),
		zap.String("status", string(existingTx.Status)))

	return nil
}

// isValidTxHash checks if a tx_hash is a real transaction hash or just an order_id fallback
func (s *AffiliateService) isValidTxHash(txHash string, orderID uuid.UUID) bool {
	// If tx_hash equals order_id string, it's a fallback value, not a real tx_hash
	return txHash != orderID.String()
}

// saveSolPriceSnapshot saves the current SOL price to database when affiliate transactions occur
// This ensures we only store price data when there's actual trading activity
func (s *AffiliateService) saveSolPriceSnapshot(ctx context.Context) error {
	s.priceMutex.RLock()
	price := s.latestSolPrice
	timestamp := s.latestSolPriceTime
	s.priceMutex.RUnlock()

	// Check if we have valid price data
	if price.IsZero() || timestamp.IsZero() {
		return fmt.Errorf("no valid SOL price data available")
	}

	// Create price snapshot
	snapshot := &model.SolPriceSnapshot{
		Price:     price,
		Timestamp: timestamp,
	}

	// Use upsert to ensure only one record per timestamp (millisecond precision)
	if err := s.affiliateRepo.UpsertSolPriceSnapshot(ctx, snapshot); err != nil {
		return fmt.Errorf("failed to upsert SOL price snapshot: %w", err)
	}

	global.GVA_LOG.Debug("SOL price snapshot saved to database",
		zap.String("price", price.String()),
		zap.Time("timestamp", timestamp))

	return nil
}

// calculateVolumeUSD calculates the volume in USD using SOL price
// It tries to use the latest price from memory first, then falls back to database
func (s *AffiliateService) calculateVolumeUSD(ctx context.Context, quoteAmount decimal.Decimal) (decimal.Decimal, error) {
	// First, try to use the latest price from memory
	s.priceMutex.RLock()
	latestPrice := s.latestSolPrice
	s.priceMutex.RUnlock()

	// If we have a valid price in memory, use it
	if !latestPrice.IsZero() {
		volumeUSD := quoteAmount.Mul(latestPrice)
		global.GVA_LOG.Debug("Calculated volume USD using memory price",
			zap.String("quote_amount", quoteAmount.String()),
			zap.String("sol_price", latestPrice.String()),
			zap.String("volume_usd", volumeUSD.String()))
		return volumeUSD, nil
	}

	// Fallback: get the latest price from database
	priceSnapshot, err := s.affiliateRepo.GetLatestSolPrice(ctx)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get SOL price from database: %w", err)
	}

	// Update memory with the database price for future use
	s.priceMutex.Lock()
	s.latestSolPrice = priceSnapshot.Price
	s.latestSolPriceTime = priceSnapshot.Timestamp
	s.priceMutex.Unlock()

	volumeUSD := quoteAmount.Mul(priceSnapshot.Price)
	global.GVA_LOG.Info("Calculated volume USD using database price (memory was empty)",
		zap.String("quote_amount", quoteAmount.String()),
		zap.String("sol_price", priceSnapshot.Price.String()),
		zap.String("volume_usd", volumeUSD.String()),
		zap.Time("price_timestamp", priceSnapshot.Timestamp))

	return volumeUSD, nil
}

// getTxHashWithLogging returns the appropriate tx_hash with detailed logging
func (s *AffiliateService) getTxHashWithLogging(txEvent *natsModel.AffiliateTxEvent) string {
	if txEvent.Txid != "" {
		global.GVA_LOG.Debug("Using actual tx_hash from NATS",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("tx_hash", txEvent.Txid),
			zap.String("status", string(txEvent.Status)))
		return txEvent.Txid
	}

	// Use order_id as fallback
	fallbackHash := txEvent.ID.String()
	global.GVA_LOG.Warn("Using order_id as tx_hash fallback",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("status", string(txEvent.Status)),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("reason", "empty_txid_from_nats"))

	return fallbackHash
}

// GetUserTransactions retrieves affiliate transactions for a user
func (s *AffiliateService) GetUserTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByUserID(ctx, userID, limit, offset)
}

// GetReferrerTransactions retrieves affiliate transactions for a referrer
func (s *AffiliateService) GetReferrerTransactions(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByReferrerID(ctx, referrerID, limit, offset)
}

// GetUnpaidCommissions retrieves unpaid commissions for a referrer
func (s *AffiliateService) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetUnpaidCommissions(ctx, referrerID)
}

// MarkCommissionsAsPaid marks commissions as paid
func (s *AffiliateService) MarkCommissionsAsPaid(ctx context.Context, transactionIDs []uint) error {
	return s.affiliateRepo.MarkCommissionAsPaid(ctx, transactionIDs)
}

// GetUserStats retrieves statistics for a user
func (s *AffiliateService) GetUserStats(ctx context.Context, userID uuid.UUID) (*UserStats, error) {
	totalVolume, err := s.affiliateRepo.GetUserTotalVolume(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user total volume: %w", err)
	}

	return &UserStats{
		UserID:      userID,
		TotalVolume: totalVolume,
	}, nil
}

// GetReferrerStats retrieves statistics for a referrer
func (s *AffiliateService) GetReferrerStats(ctx context.Context, referrerID uuid.UUID) (*ReferrerStats, error) {
	totalCommission, err := s.affiliateRepo.GetReferrerTotalCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer total commission: %w", err)
	}

	unpaidCommission, err := s.affiliateRepo.GetReferrerUnpaidCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer unpaid commission: %w", err)
	}

	return &ReferrerStats{
		ReferrerID:       referrerID,
		TotalCommission:  totalCommission,
		UnpaidCommission: unpaidCommission,
		PaidCommission:   totalCommission.Sub(unpaidCommission),
	}, nil
}

// UserStats represents user statistics
type UserStats struct {
	UserID      uuid.UUID       `json:"user_id"`
	TotalVolume decimal.Decimal `json:"total_volume"`
}

// ReferrerStats represents referrer statistics
type ReferrerStats struct {
	ReferrerID       uuid.UUID       `json:"referrer_id"`
	TotalCommission  decimal.Decimal `json:"total_commission"`
	UnpaidCommission decimal.Decimal `json:"unpaid_commission"`
	PaidCommission   decimal.Decimal `json:"paid_commission"`
}

// processMemeTradeTaskCompletion processes MEME trade task completion when affiliate transaction is completed
func (s *AffiliateService) processMemeTradeTaskCompletion(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	if affiliateTx == nil || affiliateTx.Status != model.StatusCompleted {
		global.GVA_LOG.Debug("Transaction not completed, skipping MEME trade task completion",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("status", string(affiliateTx.Status)))
		return nil
	}

	// Prepare trade data for task processing
	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     affiliateTx.VolumeUSD.InexactFloat64(),
		"order_id":   affiliateTx.OrderID.String(),
		"user_id":    affiliateTx.UserID.String(),
	}

	// Process MEME trade task completion
	global.GVA_LOG.Info("Starting MEME trade task processing",
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.Float64("volume", affiliateTx.VolumeUSD.InexactFloat64()),
		zap.Any("trade_data", tradeData))

	if err := s.taskProcessorManager.ProcessTradingEvent(ctx, affiliateTx.UserID, tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process MEME trading event",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.Error(err))
		return fmt.Errorf("failed to process MEME trading event: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completion processed",
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.Float64("volume", affiliateTx.VolumeUSD.InexactFloat64()))

	return nil
}

// processMemeTradeTaskCompletionFromNATS processes MEME trade task completion directly from NATS event
// This approach uses quote_amount > 0 instead of relying on volume_usd conversion
func (s *AffiliateService) processMemeTradeTaskCompletionFromNATS(ctx context.Context, txEvent *natsClient.AffiliateTxEvent) error {
	if txEvent == nil || txEvent.Status != "Completed" {
		return nil
	}

	// Validate that this is a valid trade (quote_amount > 0)
	if txEvent.QuoteAmount.IsZero() || txEvent.QuoteAmount.IsNegative() {
		global.GVA_LOG.Debug("Skipping task completion for zero or negative quote amount",
			zap.String("user_id", txEvent.UserId),
			zap.String("order_id", txEvent.ID.String()),
			zap.String("quote_amount", txEvent.QuoteAmount.String()))
		return nil
	}

	// Parse user ID
	userIDStr := txEvent.UserId
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Prepare trade data for task processing using quote_amount as volume
	// This bypasses the volume_usd conversion issue
	tradeData := map[string]interface{}{
		"trade_type":   "MEME",
		"volume":       txEvent.QuoteAmount.InexactFloat64(), // Use quote_amount directly
		"order_id":     txEvent.ID.String(),
		"user_id":      userIDStr,
		"quote_symbol": txEvent.QuoteSymbol,
		"quote_amount": txEvent.QuoteAmount.InexactFloat64(),
		"base_symbol":  txEvent.BaseSymbol,
		"tx_hash":      txEvent.Txid,
	}

	// Process MEME trade task completion
	global.GVA_LOG.Info("Starting MEME trade task processing from NATS event",
		zap.String("user_id", userID.String()),
		zap.String("order_id", txEvent.ID.String()),
		zap.String("quote_amount", txEvent.QuoteAmount.String()),
		zap.String("quote_symbol", txEvent.QuoteSymbol),
		zap.String("trade_type", "MEME"),
		zap.String("processing_source", "affiliate_nats_event"),
		zap.Any("trade_data", tradeData))

	if err = s.taskProcessorManager.ProcessTradingEvent(ctx, userID, tradeData); err != nil {
		global.GVA_LOG.Error("Failed to process MEME trading event from NATS",
			zap.String("user_id", userID.String()),
			zap.String("order_id", txEvent.ID.String()),
			zap.Error(err))
		return fmt.Errorf("failed to process MEME trading event from NATS: %w", err)
	}

	global.GVA_LOG.Info("MEME trade task completion processed from NATS event",
		zap.String("user_id", userID.String()),
		zap.String("order_id", txEvent.ID.String()),
		zap.String("quote_amount", txEvent.QuoteAmount.String()))

	return nil
}
